
CREATE TABLE public.cr_part_pack_temp (
	cr_part_pack_id text DEFAULT af_auid() NOT NULL,
	client_no text NOT NULL,
	part_no text NOT NULL,
	pack_type text NOT NULL,
	part_qty numeric(18, 4) DEFAULT 1 NOT NULL,
	parent_pack_type text NULL,
	pack_length int4 DEFAULT 0 NOT NULL,
	pack_width int4 DEFAULT 0 NOT NULL,
	pack_height int4 DEFAULT 0 NOT NULL,
	pack_weight_gross numeric(18, 4) DEFAULT 0 NOT NULL,
	pack_weight_net numeric(18, 4) DEFAULT 0 NOT NULL,
	ischeck_parent_pack_type bool NULL,
	is_pack_test_ok bool NULL,
	pack_rule text NULL,
	cr_part_pack_rmk01 text NULL,
	cr_part_pack_rmk02 text NULL,
	cr_part_pack_rmk03 text NULL,
	cr_part_pack_rmk04 text NULL,
	cr_part_pack_rmk05 text NULL,
	cr_part_pack_rmk06 text NULL,
	cr_part_pack_rmk07 text NULL,
	cr_part_pack_rmk08 text NULL,
	cr_part_pack_rmk09 text NULL,
	cr_part_pack_rmk10 text NULL,
	crt_time timestamp(6) NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NOT NULL,
	upd_time timestamp(6) NOT NULL,
	upd_user_no text NULL,
	upd_user text NOT NULL,
	upd_user_name text NULL,
	upd_host text NOT NULL,
	CONSTRAINT cr_part_pack_copy1_pack_rule_check CHECK ((pack_rule = ANY (ARRAY['1'::text, '2'::text, '3'::text, '4'::text, '5'::text, '6'::text]))),
	CONSTRAINT cr_part_pack_copy1_pack_type_check CHECK ((pack_type = ANY (ARRAY['00'::text, '10'::text, '30'::text, '50'::text, '70'::text]))),
	CONSTRAINT cr_part_pack_copy1_parent_pack_type_check CHECK ((parent_pack_type = ANY (ARRAY['00'::text, '10'::text, '30'::text, '50'::text, '70'::text]))),
	CONSTRAINT cr_part_pack_copy1_pkey PRIMARY KEY (cr_part_pack_id),
	CONSTRAINT cr_part_pack_copy1_client_no_fkey FOREIGN KEY (client_no) REFERENCES public.cr_client(client_no) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT cr_part_pack_copy1_part_no_fkey FOREIGN KEY (part_no) REFERENCES public.pd_part(part_no) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE INDEX cr_part_pack_part_no_idx_copy1 ON public.cr_part_pack_temp USING btree (part_no);